# 在线客服系统后台程序

基于GoFrame框架和DDD设计模式实现的在线客服系统后台程序，支持多公司、多站点、多座席的客服管理。

## 功能特性

### 核心功能
- **多公司管理**: 支持多个公司独立管理
- **座席管理**: 每个公司可以有多个座席，支持座席续费和时长管理
- **站点管理**: 一个公司可以有多个站点，站点可以分配座席
- **客服管理**: 座席与客服一对一绑定，客服可以加入技能组
- **实时通讯**: 基于WebSocket实现访客与客服的实时聊天

### 设置模块
- **邀请弹窗设置**: 配置访客邀请弹窗的样式和行为
- **咨询图标设置**: 配置咨询图标的位置、样式和动画
- **快捷对话设置**: 配置快捷对话弹窗的功能和样式
- **独立对话设置**: 配置独立对话窗口的功能
- **访客留言设置**: 配置访客留言功能和通知

### API接口
- **前端接口**: 提供给访客端使用的API接口
- **后端接口**: 提供给管理后台使用的API接口

## 技术架构

### 技术栈
- **框架**: GoFrame v2.6.4
- **数据库**: MySQL 8.0+
- **实时通讯**: WebSocket (gorilla/websocket)
- **架构模式**: DDD (领域驱动设计)

### 项目结构
```
kefu-server/
├── config/                 # 配置文件
├── internal/               # 内部代码
│   ├── cmd/               # 命令行入口
│   ├── controller/        # 控制器层
│   │   ├── frontend/      # 前端接口
│   │   └── backend/       # 后端接口
│   ├── service/           # 服务层
│   ├── dao/               # 数据访问层
│   ├── model/             # 数据模型
│   │   └── entity/        # 实体模型
│   ├── domain/            # 领域层
│   ├── infrastructure/    # 基础设施层
│   └── websocket/         # WebSocket服务
├── sql/                   # 数据库脚本
│   └── migrations/        # 数据库迁移文件
├── api/                   # API文档
├── docs/                  # 项目文档
├── go.mod                 # Go模块文件
├── main.go               # 程序入口
└── README.md             # 项目说明
```

## 数据库设计

### 核心表关系
- **公司 -> 座席** (1:多)
- **座席 -> 客服** (1:1)
- **公司 -> 站点** (1:多)
- **站点 -> 座席** (多:多，通过座席分配表)
- **技能组 -> 客服** (多:多)

### 主要数据表
- `ks_company`: 公司表
- `ks_agent`: 座席表
- `ks_site`: 站点表
- `ks_customer_service`: 客服表
- `ks_visitor`: 访客表
- `ks_chat_message`: 聊天消息表
- `ks_*_settings`: 各种设置表

## 快速开始

### 环境要求
- Go 1.21+
- MySQL 8.0+
- Redis (可选)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd kefu-server
```

2. **安装依赖**
```bash
go mod tidy
```

3. **配置数据库**
```bash
# 创建数据库
mysql -u root -p < sql/migrations/001_create_tables.sql
mysql -u root -p < sql/migrations/002_create_visitor_tables.sql
mysql -u root -p < sql/migrations/003_create_chat_tables.sql
```

4. **修改配置**
编辑 `config/config.yaml` 文件，配置数据库连接信息。

5. **启动服务**
```bash
go run main.go
```

### 服务端口
- HTTP服务: `:8080`
- WebSocket服务: `:8081`

## API接口

### 前端接口 (访客端)
- `GET /api/frontend/site/config`: 获取站点配置信息
- `POST /api/frontend/chat/init`: 初始化聊天会话
- `POST /api/frontend/chat/send`: 发送消息
- `GET /api/frontend/chat/history`: 获取聊天历史

### 后端接口 (管理端)
- 公司管理: `/api/backend/company/*`
- 座席管理: `/api/backend/seat/*`
- 站点管理: `/api/backend/site/*`
- 设置管理: `/api/backend/settings/*`
- 聊天管理: `/api/backend/chat/*`

### WebSocket接口
- 连接地址: `ws://localhost:8081/ws`
- 支持参数: `type`, `user_id`, `site_id`, `session_id`

## 开发指南

### DDD架构说明
项目采用DDD（领域驱动设计）架构，分为以下几层：

1. **Interface Layer** (接口层): 处理HTTP请求和WebSocket连接
2. **Application Layer** (应用层): 业务用例和应用服务
3. **Domain Layer** (领域层): 核心业务逻辑和领域模型
4. **Infrastructure Layer** (基础设施层): 数据持久化和外部服务

### 代码规范
- 使用GoFrame框架的标准规范
- 遵循Go语言编码规范
- 接口返回统一的JSON格式
- 错误处理使用GoFrame的错误处理机制

## 部署说明

### Docker部署
```bash
# 构建镜像
docker build -t kefu-server .

# 运行容器
docker run -d -p 8080:8080 -p 8081:8081 kefu-server
```

### 生产环境配置
- 配置数据库连接池
- 启用Redis缓存
- 配置日志输出
- 设置CORS跨域策略

## 许可证

MIT License

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题，请通过Issue或邮件联系。
