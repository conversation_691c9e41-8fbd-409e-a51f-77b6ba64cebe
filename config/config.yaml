server:
  address: ":8080"
  serverRoot: "resource"
  dumpRouterMap: true

database:
  default:
    host: "127.0.0.1"
    port: "3306"
    user: "root"
    pass: "123456"
    name: "kefu_server"
    type: "mysql"
    role: "master"
    debug: true
    prefix: "ks_"
    dryRun: false
    maxIdle: 10
    maxOpen: 100
    maxLifetime: "30s"

redis:
  default:
    address: "127.0.0.1:6379"
    db: 0
    pass: ""

websocket:
  port: ":8081"
  path: "/ws"
  readBufferSize: 1024
  writeBufferSize: 1024

logger:
  level: "all"
  stdout: true
  
jwt:
  signingKey: "kefu-server-jwt-signing-key"
  expire: 86400 # 24 hours

cors:
  allowOrigin: "*"
  allowMethods: "GET,POST,PUT,DELETE,OPTIONS"
  allowHeaders: "Origin,Content-Type,Accept,Authorization"
