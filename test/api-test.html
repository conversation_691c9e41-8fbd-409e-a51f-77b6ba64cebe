<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API接口测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .content {
            padding: 30px;
        }

        .api-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .api-section h3 {
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }

        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.1);
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .response {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .response.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .response.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .quick-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }

        .quick-btn {
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s;
        }

        .quick-btn:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .quick-btn h4 {
            color: #007bff;
            margin-bottom: 5px;
        }

        .quick-btn p {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 API接口测试工具</h1>
            <p>在线客服系统接口测试平台</p>
        </div>

        <div class="content">
            <!-- 快速测试按钮 -->
            <div class="api-section">
                <h3>🚀 快速测试</h3>
                <div class="quick-test">
                    <div class="quick-btn" onclick="quickTest('site-config')">
                        <h4>站点配置</h4>
                        <p>获取站点所有配置信息</p>
                    </div>
                    <div class="quick-btn" onclick="quickTest('company-list')">
                        <h4>公司列表</h4>
                        <p>获取公司列表数据</p>
                    </div>
                    <div class="quick-btn" onclick="quickTest('seat-list')">
                        <h4>座席列表</h4>
                        <p>获取座席管理数据</p>
                    </div>
                    <div class="quick-btn" onclick="quickTest('init-chat')">
                        <h4>初始化聊天</h4>
                        <p>创建新的聊天会话</p>
                    </div>
                    <div class="quick-btn" onclick="quickTest('send-message')">
                        <h4>发送消息</h4>
                        <p>发送聊天消息测试</p>
                    </div>
                    <div class="quick-btn" onclick="quickTest('create-company')">
                        <h4>创建公司</h4>
                        <p>创建新公司测试</p>
                    </div>
                    <div class="quick-btn" onclick="quickTest('create-session')">
                        <h4>创建会话</h4>
                        <p>创建访客会话测试</p>
                    </div>
                    <div class="quick-btn" onclick="quickTest('session-list')">
                        <h4>会话列表</h4>
                        <p>获取会话列表</p>
                    </div>
                    <div class="quick-btn" onclick="quickTest('queue-status')">
                        <h4>排队状态</h4>
                        <p>获取排队状态</p>
                    </div>
                </div>
            </div>

            <!-- 自定义测试 -->
            <div class="api-section">
                <h3>⚙️ 自定义测试</h3>

                <div class="form-row">
                    <div class="form-group">
                        <label>服务器地址:</label>
                        <input type="text" id="baseUrl" value="http://localhost:8080">
                    </div>
                    <div class="form-group">
                        <label>请求方法:</label>
                        <select id="method">
                            <option value="GET">GET</option>
                            <option value="POST">POST</option>
                            <option value="PUT">PUT</option>
                            <option value="DELETE">DELETE</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>API路径:</label>
                    <input type="text" id="apiPath" placeholder="/api/frontend/get-site-config">
                </div>

                <div class="form-group">
                    <label>请求参数 (JSON格式):</label>
                    <textarea id="requestBody" rows="6" placeholder='{"site_key": "test123"}'></textarea>
                </div>

                <div class="form-group">
                    <label>请求头 (可选):</label>
                    <textarea id="headers" rows="3" placeholder='{"Authorization": "Bearer token", "Content-Type": "application/json"}'></textarea>
                </div>

                <button onclick="sendCustomRequest()" class="btn">🚀 发送请求</button>
                <button onclick="clearResponse()" class="btn btn-success">🧹 清空响应</button>

                <div id="response" class="response"></div>
            </div>

            <!-- 批量测试 -->
            <div class="api-section">
                <h3>📊 批量测试</h3>
                <p style="margin-bottom: 15px; color: #666;">一键测试所有主要接口，检查系统整体状态</p>

                <button onclick="runBatchTest()" class="btn">🔄 运行批量测试</button>
                <button onclick="clearBatchResults()" class="btn btn-success">🧹 清空结果</button>

                <div id="batchResults" class="response"></div>
            </div>
        </div>
    </div>

    <script>
        // 预定义的测试用例
        const testCases = {
            'site-config': {
                method: 'GET',
                path: '/api/frontend/get-site-config',
                params: { site_key: 'test123' }
            },
            'company-list': {
                method: 'GET',
                path: '/api/backend/get-company-list',
                params: { page: 1, limit: 10 }
            },
            'seat-list': {
                method: 'GET',
                path: '/api/backend/get-seat-list',
                params: { company_id: 1, page: 1, limit: 10 }
            },
            'init-chat': {
                method: 'POST',
                path: '/api/frontend/init-chat',
                params: {
                    site_key: 'test123',
                    visitor_id: 'visitor_001',
                    nickname: '测试访客',
                    email: '<EMAIL>',
                    page_url: 'https://example.com',
                    page_title: '测试页面'
                }
            },
            'send-message': {
                method: 'POST',
                path: '/api/frontend/send-message',
                params: {
                    session_id: 'session_001',
                    message_type: 1,
                    content: '你好，我需要帮助'
                }
            },
            'create-company': {
                method: 'POST',
                path: '/api/backend/create-company',
                params: {
                    name: '测试公司',
                    contact_name: '张三',
                    contact_phone: '13800138000',
                    contact_email: '<EMAIL>'
                }
            },
            'create-session': {
                method: 'POST',
                path: '/api/frontend/create-session',
                params: {
                    site_id: 1,
                    company_id: 1,
                    visitor_id: 'visitor_001',
                    priority: 2,
                    source: 'web',
                    nickname: '测试访客',
                    email: '<EMAIL>',
                    page_url: 'https://example.com',
                    page_title: '测试页面',
                    need_queue: true
                }
            },
            'session-list': {
                method: 'GET',
                path: '/api/backend/get-session-list',
                params: {
                    page: 1,
                    limit: 10,
                    status: -1
                }
            },
            'queue-status': {
                method: 'GET',
                path: '/api/frontend/get-queue-status',
                params: {
                    session_id: 'session_test123'
                }
            }
        };

        // 快速测试
        function quickTest(testKey) {
            const testCase = testCases[testKey];
            if (!testCase) return;

            document.getElementById('method').value = testCase.method;
            document.getElementById('apiPath').value = testCase.path;
            document.getElementById('requestBody').value = JSON.stringify(testCase.params, null, 2);

            sendCustomRequest();
        }

        // 发送自定义请求
        async function sendCustomRequest() {
            const baseUrl = document.getElementById('baseUrl').value;
            const method = document.getElementById('method').value;
            const apiPath = document.getElementById('apiPath').value;
            const requestBodyText = document.getElementById('requestBody').value;
            const headersText = document.getElementById('headers').value;

            let url = baseUrl + apiPath;
            let options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                }
            };

            try {
                // 处理请求头
                if (headersText.trim()) {
                    const customHeaders = JSON.parse(headersText);
                    options.headers = { ...options.headers, ...customHeaders };
                }

                // 处理请求参数
                if (requestBodyText.trim()) {
                    const params = JSON.parse(requestBodyText);

                    if (method === 'GET') {
                        const urlParams = new URLSearchParams(params);
                        url += '?' + urlParams.toString();
                    } else {
                        options.body = JSON.stringify(params);
                    }
                }

                updateResponse('🚀 发送请求中...', 'info');

                const startTime = Date.now();
                const response = await fetch(url, options);
                const endTime = Date.now();
                const responseTime = endTime - startTime;

                const data = await response.json();

                const result = {
                    timestamp: new Date().toISOString(),
                    request: {
                        method: method,
                        url: url,
                        headers: options.headers,
                        body: options.body ? JSON.parse(options.body) : null
                    },
                    response: {
                        status: response.status,
                        statusText: response.statusText,
                        headers: Object.fromEntries(response.headers.entries()),
                        data: data,
                        responseTime: responseTime + 'ms'
                    }
                };

                updateResponse(JSON.stringify(result, null, 2), response.ok ? 'success' : 'error');

            } catch (error) {
                updateResponse('❌ 请求失败:\n' + error.message, 'error');
            }
        }

        // 批量测试
        async function runBatchTest() {
            const baseUrl = document.getElementById('baseUrl').value;
            const results = [];

            updateBatchResults('🔄 开始批量测试...\n', 'info');

            for (const [testName, testCase] of Object.entries(testCases)) {
                try {
                    updateBatchResults(`\n📡 测试: ${testName} (${testCase.path})\n`, 'info', true);

                    let url = baseUrl + testCase.path;
                    let options = {
                        method: testCase.method,
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    };

                    if (testCase.method === 'GET') {
                        const urlParams = new URLSearchParams(testCase.params);
                        url += '?' + urlParams.toString();
                    } else {
                        options.body = JSON.stringify(testCase.params);
                    }

                    const startTime = Date.now();
                    const response = await fetch(url, options);
                    const endTime = Date.now();
                    const responseTime = endTime - startTime;

                    const data = await response.json();

                    const result = {
                        test: testName,
                        success: response.ok,
                        status: response.status,
                        responseTime: responseTime,
                        data: data
                    };

                    results.push(result);

                    const status = response.ok ? '✅ 成功' : '❌ 失败';
                    updateBatchResults(`${status} - ${response.status} (${responseTime}ms)\n`, response.ok ? 'success' : 'error', true);

                } catch (error) {
                    results.push({
                        test: testName,
                        success: false,
                        error: error.message
                    });

                    updateBatchResults(`❌ 错误: ${error.message}\n`, 'error', true);
                }

                // 添加延迟避免请求过快
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // 生成测试报告
            const successCount = results.filter(r => r.success).length;
            const totalCount = results.length;
            const successRate = ((successCount / totalCount) * 100).toFixed(1);

            updateBatchResults(`\n📊 测试报告:\n`, 'info', true);
            updateBatchResults(`总测试数: ${totalCount}\n`, 'info', true);
            updateBatchResults(`成功数: ${successCount}\n`, 'success', true);
            updateBatchResults(`失败数: ${totalCount - successCount}\n`, 'error', true);
            updateBatchResults(`成功率: ${successRate}%\n`, 'info', true);

            updateBatchResults(`\n📋 详细结果:\n${JSON.stringify(results, null, 2)}`, 'info', true);
        }

        // 更新响应显示
        function updateResponse(text, type = 'info') {
            const responseDiv = document.getElementById('response');
            responseDiv.textContent = text;
            responseDiv.className = 'response ' + type;
        }

        function updateBatchResults(text, type = 'info', append = false) {
            const resultsDiv = document.getElementById('batchResults');
            if (append) {
                resultsDiv.textContent += text;
            } else {
                resultsDiv.textContent = text;
                resultsDiv.className = 'response ' + type;
            }
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // 清空响应
        function clearResponse() {
            document.getElementById('response').textContent = '';
            document.getElementById('response').className = 'response';
        }

        function clearBatchResults() {
            document.getElementById('batchResults').textContent = '';
            document.getElementById('batchResults').className = 'response';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateResponse('🎯 API测试工具已就绪\n\n点击上方快速测试按钮或自定义测试参数开始测试', 'info');
        });
    </script>
</body>
</html>
