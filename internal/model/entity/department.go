package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Department 部门实体
type Department struct {
	Id          uint        `json:"id"          orm:"id,primary"     description:"部门ID"`
	CompanyId   uint        `json:"company_id"  orm:"company_id"     description:"公司ID"`
	ParentId    uint        `json:"parent_id"   orm:"parent_id"      description:"上级部门ID，0表示顶级部门"`
	Name        string      `json:"name"        orm:"name"           description:"部门名称"`
	Code        string      `json:"code"        orm:"code"           description:"部门编码"`
	Description string      `json:"description" orm:"description"    description:"部门描述"`
	ManagerId   uint        `json:"manager_id"  orm:"manager_id"     description:"部门经理ID"`
	Level       int         `json:"level"       orm:"level"          description:"部门层级"`
	Sort        int         `json:"sort"        orm:"sort"           description:"排序"`
	Status      int         `json:"status"      orm:"status"         description:"状态：1-正常，0-禁用"`
	CreatedAt   *gtime.Time `json:"created_at"  orm:"created_at"     description:"创建时间"`
	UpdatedAt   *gtime.Time `json:"updated_at"  orm:"updated_at"     description:"更新时间"`
	DeletedAt   *gtime.Time `json:"deleted_at"  orm:"deleted_at"     description:"删除时间"`
}

// TableName 返回表名
func (Department) TableName() string {
	return "ks_department"
}
