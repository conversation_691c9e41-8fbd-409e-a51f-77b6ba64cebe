package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// InviteRecord 邀请访客会话记录实体
type InviteRecord struct {
	Id               uint        `json:"id"                  orm:"id,primary"            description:"邀请记录ID"`
	SiteId           uint        `json:"site_id"     orm:"site_id"     description:"站点ID"`
	VisitorId        string      `json:"visitor_id"  orm:"visitor_id"  description:"访客ID"`
	AgentId          uint        `json:"agent_id"    orm:"agent_id"    description:"席位ID"`
	InviteType       int         `json:"invite_type" orm:"invite_type" description:"邀请类型：1-主动邀请，2-自动邀请"`
	InviteMessage    string      `json:"invite_message"      orm:"invite_message"        description:"邀请消息内容"`
	InviteTime       *gtime.Time `json:"invite_time"         orm:"invite_time"           description:"邀请时间"`
	VisitorResponse  int         `json:"visitor_response"    orm:"visitor_response"      description:"访客响应：1-接受，2-拒绝，3-忽略"`
	ResponseTime     *gtime.Time `json:"response_time"       orm:"response_time"         description:"响应时间"`
	SessionId        string      `json:"session_id"          orm:"session_id"            description:"会话ID（如果访客接受邀请）"`
	VisitorIp        string      `json:"visitor_ip"          orm:"visitor_ip"            description:"访客IP地址"`
	VisitorUserAgent string      `json:"visitor_user_agent"  orm:"visitor_user_agent"    description:"访客浏览器信息"`
	VisitorPageUrl   string      `json:"visitor_page_url"    orm:"visitor_page_url"      description:"访客当前页面URL"`
	VisitorPageTitle string      `json:"visitor_page_title"  orm:"visitor_page_title"    description:"访客当前页面标题"`
	InviteTrigger    string      `json:"invite_trigger"      orm:"invite_trigger"        description:"邀请触发条件"`
	Remark           string      `json:"remark"              orm:"remark"                description:"备注"`
	CreatedAt        *gtime.Time `json:"created_at"          orm:"created_at"            description:"创建时间"`
	UpdatedAt        *gtime.Time `json:"updated_at"          orm:"updated_at"            description:"更新时间"`
}

// TableName 返回表名
func (ir *InviteRecord) TableName() string {
	return "ks_invite_record"
}

// 邀请类型常量
const (
	InviteTypeManual = 1 // 主动邀请
	InviteTypeAuto   = 2 // 自动邀请
)

// 访客响应常量
const (
	VisitorResponseAccept = 1 // 接受
	VisitorResponseReject = 2 // 拒绝
	VisitorResponseIgnore = 3 // 忽略
)
