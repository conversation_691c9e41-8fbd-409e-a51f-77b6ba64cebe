package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Employee 员工实体
type Employee struct {
	Id           uint        `json:"id"            orm:"id,primary"      description:"员工ID"`
	CompanyId    uint        `json:"company_id"    orm:"company_id"      description:"公司ID"`
	DepartmentId uint        `json:"department_id" orm:"department_id"   description:"部门ID"`
	EmployeeNo   string      `json:"employee_no"   orm:"employee_no"     description:"员工工号"`
	Username     string      `json:"username"      orm:"username"        description:"用户名"`
	Password     string      `json:"password"      orm:"password"        description:"密码"`
	RealName     string      `json:"real_name"     orm:"real_name"       description:"真实姓名"`
	Nickname     string      `json:"nickname"      orm:"nickname"        description:"昵称"`
	Avatar       string      `json:"avatar"        orm:"avatar"          description:"头像"`
	Gender       int         `json:"gender"        orm:"gender"          description:"性别：1-男，2-女，0-未知"`
	Birthday     *gtime.Time `json:"birthday"      orm:"birthday"        description:"生日"`
	Phone        string      `json:"phone"         orm:"phone"           description:"手机号"`
	Email        string      `json:"email"         orm:"email"           description:"邮箱"`
	IdCard       string      `json:"id_card"       orm:"id_card"         description:"身份证号"`
	Address      string      `json:"address"       orm:"address"         description:"地址"`
	EntryDate    *gtime.Time `json:"entry_date"    orm:"entry_date"      description:"入职日期"`
	LeaveDate    *gtime.Time `json:"leave_date"    orm:"leave_date"      description:"离职日期"`
	Position     string      `json:"position"      orm:"position"        description:"职位"`
	Level        string      `json:"level"         orm:"level"           description:"职级"`
	Salary       float64     `json:"salary"        orm:"salary"          description:"薪资"`
	Status       int         `json:"status"        orm:"status"          description:"状态：1-在职，2-离职，0-禁用"`
	Remark       string      `json:"remark"        orm:"remark"          description:"备注"`
	CreatedAt    *gtime.Time `json:"created_at"    orm:"created_at"      description:"创建时间"`
	UpdatedAt    *gtime.Time `json:"updated_at"    orm:"updated_at"      description:"更新时间"`
	DeletedAt    *gtime.Time `json:"deleted_at"    orm:"deleted_at"      description:"删除时间"`
}

// TableName 返回表名
func (Employee) TableName() string {
	return "ks_employee"
}
