package entity

import "github.com/gogf/gf/v2/os/gtime"

// SessionEvaluation 会话评价表
type SessionEvaluation struct {
	Id                uint64      `json:"id" orm:"id,primary"`                           // 评价ID
	SessionId         string      `json:"session_id" orm:"session_id"`                   // 会话ID
	VisitorId         string      `json:"visitor_id" orm:"visitor_id"`                   // 访客ID
	CustomerServiceId uint64      `json:"customer_service_id" orm:"customer_service_id"` // 客服ID
	OverallScore      int         `json:"overall_score" orm:"overall_score"`             // 总体评分：1-5分
	ServiceScore      int         `json:"service_score" orm:"service_score"`             // 服务评分：1-5分
	ResponseScore     int         `json:"response_score" orm:"response_score"`           // 响应评分：1-5分
	ProfessionalScore int         `json:"professional_score" orm:"professional_score"`   // 专业评分：1-5分
	Comment           string      `json:"comment" orm:"comment"`                         // 评价内容
	Tags              string      `json:"tags" orm:"tags"`                               // 评价标签(JSON格式)
	IsAnonymous       int         `json:"is_anonymous" orm:"is_anonymous"`               // 是否匿名：1-是，0-否
	EvaluationTime    *gtime.Time `json:"evaluation_time" orm:"evaluation_time"`         // 评价时间
	CreatedAt         *gtime.Time `json:"created_at" orm:"created_at"`                   // 创建时间
	UpdatedAt         *gtime.Time `json:"updated_at" orm:"updated_at"`                   // 更新时间
}

// TableName 返回表名
func (se *SessionEvaluation) TableName() string {
	return "ks_session_evaluation"
}
