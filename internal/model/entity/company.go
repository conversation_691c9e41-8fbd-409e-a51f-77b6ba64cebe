package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Company 公司表
type Company struct {
	Id         uint64      `json:"id" orm:"id,primary"`           // 公司ID
	Name       string      `json:"name" orm:"name"`               // 公司名称
	Introduce  string      `json:"introduce" orm:"introduce"`     // 公司简介
	Address    string      `json:"address" orm:"address"`         // 公司地址
	Email      string      `json:"email" orm:"email"`             // 邮箱地址
	Telphone   string      `json:"telphone" orm:"telphone"`       // 固定电话
	Logo       string      `json:"logo" orm:"logo"`               // 公司Logo
	Ownerid    uint64      `json:"owner_id" orm:"owner_id"`       // 超级管理员
	Status     int         `json:"status" orm:"status"`           // 状态：1-正常，0-禁用
	ExpireTime *gtime.Time `json:"expire_time" orm:"expire_time"` // 过期时间
	CreatedAt  *gtime.Time `json:"created_at" orm:"created_at"`   // 创建时间
	UpdatedAt  *gtime.Time `json:"updated_at" orm:"updated_at"`   // 更新时间
}

// TableName 返回表名
func (c *Company) TableName() string {
	return "ks_company"
}
