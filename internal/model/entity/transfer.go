package entity

import "github.com/gogf/gf/v2/os/gtime"

// SessionTransfer 会话转接记录表
type SessionTransfer struct {
	Id                    uint64      `json:"id" orm:"id,primary"`                                     // 转接ID
	SessionId             string      `json:"session_id" orm:"session_id"`                             // 会话ID
	FromCustomerServiceId uint64      `json:"from_customer_service_id" orm:"from_customer_service_id"` // 原客服ID
	ToCustomerServiceId   uint64      `json:"to_customer_service_id" orm:"to_customer_service_id"`     // 目标客服ID
	FromSkillGroupId      uint64      `json:"from_skill_group_id" orm:"from_skill_group_id"`           // 原技能组ID
	ToSkillGroupId        uint64      `json:"to_skill_group_id" orm:"to_skill_group_id"`               // 目标技能组ID
	TransferType          int         `json:"transfer_type" orm:"transfer_type"`                       // 转接类型：1-客服转接，2-技能组转接，3-系统转接
	TransferReason        string      `json:"transfer_reason" orm:"transfer_reason"`                   // 转接原因
	Status                int         `json:"status" orm:"status"`                                     // 转接状态：0-待接受，1-已接受，2-已拒绝，3-超时
	TransferTime          *gtime.Time `json:"transfer_time" orm:"transfer_time"`                       // 转接时间
	AcceptTime            *gtime.Time `json:"accept_time" orm:"accept_time"`                           // 接受时间
	RejectReason          string      `json:"reject_reason" orm:"reject_reason"`                       // 拒绝原因
	CreatedAt             *gtime.Time `json:"created_at" orm:"created_at"`                             // 创建时间
	UpdatedAt             *gtime.Time `json:"updated_at" orm:"updated_at"`                             // 更新时间
}

// TableName 返回表名
func (st *SessionTransfer) TableName() string {
	return "ks_session_transfer"
}
