package frontend

import (
	"kefu-server/internal/service"
	"kefu-server/internal/service/session"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type SessionController struct{}

func NewSessionController() *SessionController {
	return &SessionController{}
}

// CreateSession 创建访客会话
// POST /api/frontend/session/create
func (c *SessionController) CreateSession(r *ghttp.Request) {
	var req session.CreateSessionReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 创建会话
	sessionEntity, err := service.Session().CreateSession(r.Context(), &req)
	if err != nil {
		g.Log().Error(r.Context(), "创建会话失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "创建会话失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "创建成功",
		"data": sessionEntity,
	})
}

// JoinQueue 加入排队
// POST /api/frontend/session/join-queue
func (c *SessionController) JoinQueue(r *ghttp.Request) {
	var req session.JoinQueueReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 加入排队
	queue, err := service.Session().JoinQueue(r.Context(), &req)
	if err != nil {
		g.Log().Error(r.Context(), "加入排队失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "加入排队失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "加入排队成功",
		"data": queue,
	})
}

// GetQueueStatus 获取排队状态
// GET /api/frontend/session/queue-status?session_id=xxx
func (c *SessionController) GetQueueStatus(r *ghttp.Request) {
	sessionId := r.Get("session_id").String()
	if sessionId == "" {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "会话ID不能为空",
			"data": nil,
		})
	}

	// 暂时返回模拟数据
	mockStatus := g.Map{
		"queue_position": 3,
		"estimated_wait": 540, // 9分钟
		"status":         0,   // 排队中
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": mockStatus,
	})
}

// EvaluateSession 评价会话
// POST /api/frontend/session/evaluate
func (c *SessionController) EvaluateSession(r *ghttp.Request) {
	var req session.EvaluateSessionReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 暂时返回成功
	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "评价成功",
		"data": nil,
	})
}

// EndSession 结束会话
// POST /api/frontend/session/end
func (c *SessionController) EndSession(r *ghttp.Request) {
	var req session.EndSessionReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 结束会话
	err := service.Session().EndSession(r.Context(), &req)
	if err != nil {
		g.Log().Error(r.Context(), "结束会话失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "结束会话失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "会话已结束",
		"data": nil,
	})
}
