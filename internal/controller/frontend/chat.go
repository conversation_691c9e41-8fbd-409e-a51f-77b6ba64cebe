package frontend

import (
	"kefu-server/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type ChatController struct{}

func NewChatController() *ChatController {
	return &ChatController{}
}

// InitChat 初始化聊天
// POST /api/frontend/chat/init
func (c *ChatController) InitChat(r *ghttp.Request) {
	ctx := r.Context()

	var req InitChatReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 初始化聊天会话
	session, err := service.Chat().InitChatSession(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "初始化聊天失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "初始化聊天失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "初始化成功",
		"data": session,
	})
}

// SendMessage 发送消息
// POST /api/frontend/chat/send
func (c *ChatController) SendMessage(r *ghttp.Request) {
	ctx := r.Context()

	var req SendMessageReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 发送消息
	message, err := service.Chat().SendMessage(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "发送消息失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "发送消息失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "发送成功",
		"data": message,
	})
}

// GetChatHistory 获取聊天历史
// GET /api/frontend/chat/history?session_id=xxx&page=1&limit=20
func (c *ChatController) GetChatHistory(r *ghttp.Request) {
	ctx := r.Context()

	sessionId := r.Get("session_id").String()
	page := r.Get("page", 1).Int()
	limit := r.Get("limit", 20).Int()

	if sessionId == "" {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "会话ID不能为空",
			"data": nil,
		})
	}

	// 获取聊天历史
	history, total, err := service.Chat().GetChatHistory(ctx, sessionId, page, limit)
	if err != nil {
		g.Log().Error(ctx, "获取聊天历史失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取聊天历史失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": g.Map{
			"list":  history,
			"total": total,
			"page":  page,
			"limit": limit,
		},
	})
}

// InitChatReq 初始化聊天请求
type InitChatReq struct {
	SiteKey   string `json:"site_key" v:"required#站点密钥不能为空"`
	VisitorId string `json:"visitor_id" v:"required#访客ID不能为空"`
	Nickname  string `json:"nickname"`
	Avatar    string `json:"avatar"`
	Email     string `json:"email"`
	Phone     string `json:"phone"`
	PageUrl   string `json:"page_url"`
	PageTitle string `json:"page_title"`
	Referrer  string `json:"referrer"`
}

// SendMessageReq 发送消息请求
type SendMessageReq struct {
	SessionId   string `json:"session_id" v:"required#会话ID不能为空"`
	MessageType int    `json:"message_type" v:"required|in:1,2,3#消息类型不能为空|消息类型无效"`
	Content     string `json:"content"`
	FileUrl     string `json:"file_url"`
	FileName    string `json:"file_name"`
	FileSize    int64  `json:"file_size"`
}
