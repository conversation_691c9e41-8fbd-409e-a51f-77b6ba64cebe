package backend

import (
	"kefu-server/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type SettingsController struct{}

func NewSettingsController() *SettingsController {
	return &SettingsController{}
}

// UpdateSiteSettings 更新站点设置
// PUT /api/backend/settings/site
func (c *SettingsController) UpdateSiteSettings(r *ghttp.Request) {
	ctx := r.Context()

	var req UpdateSiteSettingsReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 更新站点设置
	err := service.Settings().UpdateSiteSettings(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "更新站点设置失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "更新站点设置失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "更新成功",
		"data": nil,
	})
}

// UpdateInvitePopupSettings 更新邀请弹窗设置
// PUT /api/backend/settings/invite-popup
func (c *SettingsController) UpdateInvitePopupSettings(r *ghttp.Request) {
	ctx := r.Context()

	var req UpdateInvitePopupSettingsReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 更新邀请弹窗设置
	err := service.Settings().UpdateInvitePopupSettings(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "更新邀请弹窗设置失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "更新邀请弹窗设置失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "更新成功",
		"data": nil,
	})
}

// UpdateConsultIconSettings 更新咨询图标设置
// PUT /api/backend/settings/consult-icon
func (c *SettingsController) UpdateConsultIconSettings(r *ghttp.Request) {
	ctx := r.Context()

	var req UpdateConsultIconSettingsReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 更新咨询图标设置
	err := service.Settings().UpdateConsultIconSettings(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "更新咨询图标设置失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "更新咨询图标设置失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "更新成功",
		"data": nil,
	})
}

// UpdateQuickChatSettings 更新快捷对话设置
// PUT /api/backend/settings/quick-chat
func (c *SettingsController) UpdateQuickChatSettings(r *ghttp.Request) {
	ctx := r.Context()

	var req UpdateQuickChatSettingsReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 更新快捷对话设置
	err := service.Settings().UpdateQuickChatSettings(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "更新快捷对话设置失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "更新快捷对话设置失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "更新成功",
		"data": nil,
	})
}

// UpdateIndependentChatSettings 更新独立对话设置
// PUT /api/backend/settings/independent-chat
func (c *SettingsController) UpdateIndependentChatSettings(r *ghttp.Request) {
	ctx := r.Context()

	var req UpdateIndependentChatSettingsReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 更新独立对话设置
	err := service.Settings().UpdateIndependentChatSettings(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "更新独立对话设置失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "更新独立对话设置失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "更新成功",
		"data": nil,
	})
}

// UpdateVisitorMessageSettings 更新访客留言设置
// PUT /api/backend/settings/visitor-message
func (c *SettingsController) UpdateVisitorMessageSettings(r *ghttp.Request) {
	ctx := r.Context()

	var req UpdateVisitorMessageSettingsReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 更新访客留言设置
	err := service.Settings().UpdateVisitorMessageSettings(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "更新访客留言设置失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "更新访客留言设置失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "更新成功",
		"data": nil,
	})
}

// 请求结构体定义
type UpdateSiteSettingsReq struct {
	SiteId           uint64 `json:"site_id" v:"required#站点ID不能为空"`
	WelcomeMessage   string `json:"welcome_message"`
	OfflineMessage   string `json:"offline_message"`
	AutoReplyEnabled int    `json:"auto_reply_enabled" v:"in:0,1#自动回复启用状态无效"`
	AutoReplyMessage string `json:"auto_reply_message"`
	WorkingHours     string `json:"working_hours"`
}

type UpdateInvitePopupSettingsReq struct {
	SiteId        uint64 `json:"site_id" v:"required#站点ID不能为空"`
	Enabled       int    `json:"enabled" v:"in:0,1#启用状态无效"`
	Title         string `json:"title"`
	Content       string `json:"content"`
	ShowDelay     int    `json:"show_delay"`
	ShowFrequency int    `json:"show_frequency" v:"in:1,2,3#显示频率无效"`
	Position      string `json:"position"`
	Style         string `json:"style"`
}

type UpdateConsultIconSettingsReq struct {
	SiteId    uint64 `json:"site_id" v:"required#站点ID不能为空"`
	Enabled   int    `json:"enabled" v:"in:0,1#启用状态无效"`
	IconType  string `json:"icon_type"`
	IconUrl   string `json:"icon_url"`
	Position  string `json:"position"`
	OffsetX   int    `json:"offset_x"`
	OffsetY   int    `json:"offset_y"`
	Size      string `json:"size"`
	Animation string `json:"animation"`
	Style     string `json:"style"`
}

type UpdateQuickChatSettingsReq struct {
	SiteId           uint64 `json:"site_id" v:"required#站点ID不能为空"`
	Enabled          int    `json:"enabled" v:"in:0,1#启用状态无效"`
	Title            string `json:"title"`
	Width            int    `json:"width"`
	Height           int    `json:"height"`
	Position         string `json:"position"`
	ShowAvatar       int    `json:"show_avatar" v:"in:0,1#显示头像状态无效"`
	ShowNickname     int    `json:"show_nickname" v:"in:0,1#显示昵称状态无效"`
	ShowOnlineStatus int    `json:"show_online_status" v:"in:0,1#显示在线状态无效"`
	QuickReplies     string `json:"quick_replies"`
	Style            string `json:"style"`
}

type UpdateIndependentChatSettingsReq struct {
	SiteId           uint64 `json:"site_id" v:"required#站点ID不能为空"`
	Enabled          int    `json:"enabled" v:"in:0,1#启用状态无效"`
	WindowTitle      string `json:"window_title"`
	Width            int    `json:"width"`
	Height           int    `json:"height"`
	Resizable        int    `json:"resizable" v:"in:0,1#可调整大小状态无效"`
	ShowToolbar      int    `json:"show_toolbar" v:"in:0,1#显示工具栏状态无效"`
	ShowEmoji        int    `json:"show_emoji" v:"in:0,1#显示表情状态无效"`
	ShowFileUpload   int    `json:"show_file_upload" v:"in:0,1#显示文件上传状态无效"`
	ShowHistory      int    `json:"show_history" v:"in:0,1#显示历史记录状态无效"`
	MaxMessageLength int    `json:"max_message_length"`
	Style            string `json:"style"`
}

type UpdateVisitorMessageSettingsReq struct {
	SiteId            uint64 `json:"site_id" v:"required#站点ID不能为空"`
	Enabled           int    `json:"enabled" v:"in:0,1#启用状态无效"`
	Title             string `json:"title"`
	RequiredFields    string `json:"required_fields"`
	OptionalFields    string `json:"optional_fields"`
	MaxMessageLength  int    `json:"max_message_length"`
	AutoReplyEnabled  int    `json:"auto_reply_enabled" v:"in:0,1#自动回复启用状态无效"`
	AutoReplyMessage  string `json:"auto_reply_message"`
	EmailNotification int    `json:"email_notification" v:"in:0,1#邮件通知状态无效"`
	NotificationEmail string `json:"notification_email"`
	Style             string `json:"style"`
}
