package backend

import (
	"kefu-server/internal/service"
	"kefu-server/internal/service/session"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type SessionController struct{}

func NewSessionController() *SessionController {
	return &SessionController{}
}

// GetSessionList 获取会话列表
// GET /api/backend/session/list
func (c *SessionController) GetSessionList(r *ghttp.Request) {
	var req session.GetSessionListReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 获取会话列表
	list, total, err := service.Session().GetSessionList(r.Context(), &req)
	if err != nil {
		g.Log().Error(r.Context(), "获取会话列表失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取会话列表失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": g.Map{
			"list":  list,
			"total": total,
			"page":  req.Page,
			"limit": req.Limit,
		},
	})
}

// AssignSession 分配会话
// POST /api/backend/session/assign
func (c *SessionController) AssignSession(r *ghttp.Request) {
	var req session.AssignSessionReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 分配会话
	err := service.Session().AssignSession(r.Context(), &req)
	if err != nil {
		g.Log().Error(r.Context(), "分配会话失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "分配会话失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "分配成功",
		"data": nil,
	})
}

// TransferSession 转接会话
// POST /api/backend/session/transfer
func (c *SessionController) TransferSession(r *ghttp.Request) {
	var req session.TransferSessionReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 转接会话
	err := service.Session().TransferSession(r.Context(), &req)
	if err != nil {
		g.Log().Error(r.Context(), "转接会话失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "转接会话失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "转接成功",
		"data": nil,
	})
}

// EndSession 结束会话
// POST /api/backend/session/end
func (c *SessionController) EndSession(r *ghttp.Request) {
	var req session.EndSessionReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 结束会话
	err := service.Session().EndSession(r.Context(), &req)
	if err != nil {
		g.Log().Error(r.Context(), "结束会话失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "结束会话失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "会话已结束",
		"data": nil,
	})
}

// GetSessionStatistics 获取会话统计
// GET /api/backend/session/statistics
func (c *SessionController) GetSessionStatistics(r *ghttp.Request) {
	var req session.SessionStatisticsReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 暂时返回模拟统计数据
	mockStats := g.Map{
		"total_sessions":        156,
		"completed_sessions":    142,
		"average_wait_time":     125.5,
		"average_session_time":  480.2,
		"average_response_time": 15.8,
		"satisfaction_rate":     4.2,
		"transfer_rate":         0.08,
		"abandon_rate":          0.05,
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": mockStats,
	})
}

// GetSessionDetail 获取会话详情
// GET /api/backend/session/detail/:session_id
func (c *SessionController) GetSessionDetail(r *ghttp.Request) {
	sessionId := r.Get("session_id").String()
	if sessionId == "" {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "会话ID不能为空",
			"data": nil,
		})
	}

	// 暂时返回模拟详情数据
	mockDetail := g.Map{
		"session": g.Map{
			"id":                   1,
			"session_id":           sessionId,
			"visitor_id":           "visitor_001",
			"customer_service_id":  1,
			"status":               2,
			"start_time":           "2024-01-15 10:30:00",
			"end_time":             "2024-01-15 10:45:00",
			"duration":             900,
			"message_count":        15,
			"satisfaction":         5,
		},
		"visitor": g.Map{
			"visitor_id": "visitor_001",
			"nickname":   "访客001",
			"email":      "<EMAIL>",
		},
		"service": g.Map{
			"id":       1,
			"nickname": "客服小王",
			"avatar":   "/avatars/service1.jpg",
		},
		"messages": []g.Map{
			{
				"id":           1,
				"sender_type":  1,
				"content":      "你好，我需要帮助",
				"created_at":   "2024-01-15 10:30:15",
			},
			{
				"id":           2,
				"sender_type":  2,
				"content":      "您好！我是客服小王，很高兴为您服务",
				"created_at":   "2024-01-15 10:30:30",
			},
		},
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": mockDetail,
	})
}
