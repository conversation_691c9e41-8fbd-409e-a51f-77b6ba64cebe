package backend

import (
	"kefu-server/internal/service"
	"kefu-server/internal/service/department"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type DepartmentController struct{}

func NewDepartmentController() *DepartmentController {
	return &DepartmentController{}
}

// CreateDepartment 创建部门
// POST /api/backend/create-department
func (c *DepartmentController) CreateDepartment(r *ghttp.Request) {
	ctx := r.Context()
	var req department.CreateDepartmentReq

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误: " + err.Error(),
			"data": nil,
		})
	}

	dept, err := service.Department().CreateDepartment(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "创建部门失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.<PERSON>rror(),
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "创建成功",
		"data": dept,
	})
}

// UpdateDepartment 更新部门
// POST /api/backend/update-department
func (c *DepartmentController) UpdateDepartment(r *ghttp.Request) {
	ctx := r.Context()
	var req department.UpdateDepartmentReq

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误: " + err.Error(),
			"data": nil,
		})
	}

	err := service.Department().UpdateDepartment(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "更新部门失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "更新成功",
		"data": nil,
	})
}

// DeleteDepartment 删除部门
// POST /api/backend/delete-department
func (c *DepartmentController) DeleteDepartment(r *ghttp.Request) {
	ctx := r.Context()
	id := r.Get("id").Uint()
	companyId := r.Get("company_id").Uint()

	if id == 0 || companyId == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
			"data": nil,
		})
	}

	err := service.Department().DeleteDepartment(ctx, id, companyId)
	if err != nil {
		g.Log().Error(ctx, "删除部门失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "删除成功",
		"data": nil,
	})
}

// GetDepartmentDetail 获取部门详情
// GET /api/backend/get-department-detail
func (c *DepartmentController) GetDepartmentDetail(r *ghttp.Request) {
	ctx := r.Context()
	id := r.Get("id").Uint()
	companyId := r.Get("company_id").Uint()

	if id == 0 || companyId == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
			"data": nil,
		})
	}

	dept, err := service.Department().GetDepartmentById(ctx, id, companyId)
	if err != nil {
		g.Log().Error(ctx, "获取部门详情失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取部门详情失败",
			"data": nil,
		})
		return
	}

	if dept == nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 404,
			"msg":  "部门不存在",
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": dept,
	})
}

// GetDepartmentList 获取部门列表
// GET /api/backend/get-department-list
func (c *DepartmentController) GetDepartmentList(r *ghttp.Request) {
	ctx := r.Context()
	var req department.GetDepartmentListReq

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误: " + err.Error(),
			"data": nil,
		})
	}

	departments, total, err := service.Department().GetDepartmentList(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "获取部门列表失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取部门列表失败",
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": g.Map{
			"list":  departments,
			"total": total,
			"page":  req.Page,
			"limit": req.Limit,
		},
	})
}

// GetDepartmentTree 获取部门树
// GET /api/backend/get-department-tree
func (c *DepartmentController) GetDepartmentTree(r *ghttp.Request) {
	ctx := r.Context()
	companyId := r.Get("company_id").Uint()

	if companyId == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "公司ID不能为空",
			"data": nil,
		})
	}

	tree, err := service.Department().GetDepartmentTree(ctx, companyId)
	if err != nil {
		g.Log().Error(ctx, "获取部门树失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取部门树失败",
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": tree,
	})
}
