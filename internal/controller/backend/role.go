package backend

import (
	"kefu-server/internal/service"
	"kefu-server/internal/service/role"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type RoleController struct{}

func NewRoleController() *RoleController {
	return &RoleController{}
}

// CreateRole 创建角色
// POST /api/backend/create-role
func (c *RoleController) CreateRole(r *ghttp.Request) {
	ctx := r.Context()
	var req role.CreateRoleReq

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误: " + err.Error(),
			"data": nil,
		})
	}

	roleEntity, err := service.Role().CreateRole(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "创建角色失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "创建成功",
		"data": roleEntity,
	})
}

// UpdateRole 更新角色
// POST /api/backend/update-role
func (c *RoleController) UpdateRole(r *ghttp.Request) {
	ctx := r.Context()
	var req role.UpdateRoleReq

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误: " + err.Error(),
			"data": nil,
		})
	}

	err := service.Role().UpdateRole(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "更新角色失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "更新成功",
		"data": nil,
	})
}

// DeleteRole 删除角色
// POST /api/backend/delete-role
func (c *RoleController) DeleteRole(r *ghttp.Request) {
	ctx := r.Context()
	id := r.Get("id").Uint()
	companyId := r.Get("company_id").Uint()

	if id == 0 || companyId == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
			"data": nil,
		})
	}

	err := service.Role().DeleteRole(ctx, id, companyId)
	if err != nil {
		g.Log().Error(ctx, "删除角色失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "删除成功",
		"data": nil,
	})
}

// GetRoleDetail 获取角色详情
// GET /api/backend/get-role-detail
func (c *RoleController) GetRoleDetail(r *ghttp.Request) {
	ctx := r.Context()
	id := r.Get("id").Uint()
	companyId := r.Get("company_id").Uint()

	if id == 0 || companyId == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
			"data": nil,
		})
	}

	roleEntity, err := service.Role().GetRoleById(ctx, id, companyId)
	if err != nil {
		g.Log().Error(ctx, "获取角色详情失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取角色详情失败",
			"data": nil,
		})
		return
	}

	if roleEntity == nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 404,
			"msg":  "角色不存在",
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": roleEntity,
	})
}

// GetRoleList 获取角色列表
// GET /api/backend/get-role-list
func (c *RoleController) GetRoleList(r *ghttp.Request) {
	ctx := r.Context()
	var req role.GetRoleListReq

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误: " + err.Error(),
			"data": nil,
		})
	}

	roles, total, err := service.Role().GetRoleList(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "获取角色列表失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取角色列表失败",
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": g.Map{
			"list":  roles,
			"total": total,
			"page":  req.Page,
			"limit": req.Limit,
		},
	})
}

// AssignRoleToEmployee 为员工分配角色
// POST /api/backend/assign-role-to-employee
func (c *RoleController) AssignRoleToEmployee(r *ghttp.Request) {
	ctx := r.Context()
	employeeId := r.Get("employee_id").Uint()
	roleIds := r.Get("role_ids").Uints()

	if employeeId == 0 || len(roleIds) == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
			"data": nil,
		})
	}

	err := service.Role().AssignRoleToEmployee(ctx, employeeId, roleIds)
	if err != nil {
		g.Log().Error(ctx, "分配角色失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "分配成功",
		"data": nil,
	})
}

// RemoveRoleFromEmployee 移除员工角色
// POST /api/backend/remove-role-from-employee
func (c *RoleController) RemoveRoleFromEmployee(r *ghttp.Request) {
	ctx := r.Context()
	employeeId := r.Get("employee_id").Uint()
	roleId := r.Get("role_id").Uint()

	if employeeId == 0 || roleId == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
			"data": nil,
		})
	}

	err := service.Role().RemoveRoleFromEmployee(ctx, employeeId, roleId)
	if err != nil {
		g.Log().Error(ctx, "移除角色失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "移除成功",
		"data": nil,
	})
}
