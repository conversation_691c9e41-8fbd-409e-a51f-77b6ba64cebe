package backend

import (
	"kefu-server/internal/service"
	"kefu-server/internal/service/employee"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type EmployeeController struct{}

func NewEmployeeController() *EmployeeController {
	return &EmployeeController{}
}

// CreateEmployee 创建员工
// POST /api/backend/create-employee
func (c *EmployeeController) CreateEmployee(r *ghttp.Request) {
	ctx := r.Context()
	var req employee.CreateEmployeeReq

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误: " + err.Error(),
			"data": nil,
		})
	}

	emp, err := service.Employee().CreateEmployee(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "创建员工失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "创建成功",
		"data": emp,
	})
}

// UpdateEmployee 更新员工
// POST /api/backend/update-employee
func (c *EmployeeController) UpdateEmployee(r *ghttp.Request) {
	ctx := r.Context()
	var req employee.UpdateEmployeeReq

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误: " + err.Error(),
			"data": nil,
		})
	}

	err := service.Employee().UpdateEmployee(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "更新员工失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "更新成功",
		"data": nil,
	})
}

// DeleteEmployee 删除员工
// POST /api/backend/delete-employee
func (c *EmployeeController) DeleteEmployee(r *ghttp.Request) {
	ctx := r.Context()
	id := r.Get("id").Uint()
	companyId := r.Get("company_id").Uint()

	if id == 0 || companyId == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
			"data": nil,
		})
	}

	err := service.Employee().DeleteEmployee(ctx, id, companyId)
	if err != nil {
		g.Log().Error(ctx, "删除员工失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "删除成功",
		"data": nil,
	})
}

// GetEmployeeDetail 获取员工详情
// GET /api/backend/get-employee-detail
func (c *EmployeeController) GetEmployeeDetail(r *ghttp.Request) {
	ctx := r.Context()
	id := r.Get("id").Uint()
	companyId := r.Get("company_id").Uint()

	if id == 0 || companyId == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误",
			"data": nil,
		})
	}

	emp, err := service.Employee().GetEmployeeById(ctx, id, companyId)
	if err != nil {
		g.Log().Error(ctx, "获取员工详情失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取员工详情失败",
			"data": nil,
		})
		return
	}

	if emp == nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 404,
			"msg":  "员工不存在",
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": emp,
	})
}

// GetEmployeeList 获取员工列表
// GET /api/backend/get-employee-list
func (c *EmployeeController) GetEmployeeList(r *ghttp.Request) {
	ctx := r.Context()
	var req employee.GetEmployeeListReq

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误: " + err.Error(),
			"data": nil,
		})
	}

	employees, total, err := service.Employee().GetEmployeeList(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "获取员工列表失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取员工列表失败",
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": g.Map{
			"list":  employees,
			"total": total,
			"page":  req.Page,
			"limit": req.Limit,
		},
	})
}

// RegisterUser 用户注册
// POST /api/backend/register-user
func (c *EmployeeController) RegisterUser(r *ghttp.Request) {
	ctx := r.Context()
	var req employee.RegisterUserReq

	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数错误: " + err.Error(),
			"data": nil,
		})
		return
	}

	result, err := service.Employee().RegisterUser(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "用户注册失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "注册成功",
		"data": result,
	})
}
