package session

// CreateSessionReq 创建会话请求
type CreateSessionReq struct {
	SiteId       uint64 `json:"site_id" v:"required#站点ID不能为空"`
	CompanyId    uint64 `json:"company_id" v:"required#公司ID不能为空"`
	VisitorId    string `json:"visitor_id" v:"required#访客ID不能为空"`
	SkillGroupId uint64 `json:"skill_group_id"`
	Priority     int    `json:"priority" v:"in:1,2,3,4#优先级值无效"`
	Source       string `json:"source" v:"in:web,mobile,api#来源值无效"`
	Nickname     string `json:"nickname"`
	Avatar       string `json:"avatar"`
	Email        string `json:"email"`
	Phone        string `json:"phone"`
	IpAddress    string `json:"ip_address"`
	UserAgent    string `json:"user_agent"`
	Referrer     string `json:"referrer"`
	PageUrl      string `json:"page_url"`
	PageTitle    string `json:"page_title"`
	NeedQueue    bool   `json:"need_queue"`
}

// JoinQueueReq 加入排队请求
type JoinQueueReq struct {
	SessionId    string `json:"session_id" v:"required#会话ID不能为空"`
	SiteId       uint64 `json:"site_id" v:"required#站点ID不能为空"`
	CompanyId    uint64 `json:"company_id" v:"required#公司ID不能为空"`
	VisitorId    string `json:"visitor_id" v:"required#访客ID不能为空"`
	SkillGroupId uint64 `json:"skill_group_id"`
	Priority     int    `json:"priority" v:"in:1,2,3,4#优先级值无效"`
}

// AssignSessionReq 分配会话请求
type AssignSessionReq struct {
	SessionId    string `json:"session_id" v:"required#会话ID不能为空"`
	AgentId      uint64 `json:"agent_id" v:"required#席位ID不能为空"`
	SkillGroupId uint64 `json:"skill_group_id"`
}

// EndSessionReq 结束会话请求
type EndSessionReq struct {
	SessionId string `json:"session_id" v:"required#会话ID不能为空"`
	EndReason string `json:"end_reason"`
}

// TransferSessionReq 转接会话请求
type TransferSessionReq struct {
	SessionId             string `json:"session_id" v:"required#会话ID不能为空"`
	FromCustomerServiceId uint64 `json:"from_customer_service_id" v:"required#原客服ID不能为空"`
	ToCustomerServiceId   uint64 `json:"to_customer_service_id" v:"required#目标客服ID不能为空"`
	FromSkillGroupId      uint64 `json:"from_skill_group_id"`
	ToSkillGroupId        uint64 `json:"to_skill_group_id"`
	TransferType          int    `json:"transfer_type" v:"required|in:1,2,3#转接类型不能为空|转接类型无效"`
	TransferReason        string `json:"transfer_reason"`
}

// GetSessionListReq 获取会话列表请求
type GetSessionListReq struct {
	SiteId            uint64 `json:"site_id"`
	CompanyId         uint64 `json:"company_id"`
	CustomerServiceId uint64 `json:"customer_service_id"`
	Status            int    `json:"status" v:"in:-1,0,1,2,3,4#状态值无效"`
	StartTime         string `json:"start_time"`
	EndTime           string `json:"end_time"`
	Page              int    `json:"page" v:"required|min:1#页码不能为空|页码必须大于0"`
	Limit             int    `json:"limit" v:"required|min:1|max:100#每页数量不能为空|每页数量必须大于0|每页数量不能超过100"`
}

// EvaluateSessionReq 评价会话请求
type EvaluateSessionReq struct {
	SessionId         string   `json:"session_id" v:"required#会话ID不能为空"`
	VisitorId         string   `json:"visitor_id" v:"required#访客ID不能为空"`
	CustomerServiceId uint64   `json:"customer_service_id" v:"required#客服ID不能为空"`
	OverallScore      int      `json:"overall_score" v:"required|in:1,2,3,4,5#总体评分不能为空|总体评分必须在1-5之间"`
	ServiceScore      int      `json:"service_score" v:"in:1,2,3,4,5#服务评分必须在1-5之间"`
	ResponseScore     int      `json:"response_score" v:"in:1,2,3,4,5#响应评分必须在1-5之间"`
	ProfessionalScore int      `json:"professional_score" v:"in:1,2,3,4,5#专业评分必须在1-5之间"`
	Comment           string   `json:"comment"`
	Tags              []string `json:"tags"`
	IsAnonymous       bool     `json:"is_anonymous"`
}

// GetQueueStatusReq 获取排队状态请求
type GetQueueStatusReq struct {
	SessionId string `json:"session_id" v:"required#会话ID不能为空"`
}

// GetQueueStatusResp 获取排队状态响应
type GetQueueStatusResp struct {
	QueuePosition int `json:"queue_position"` // 排队位置
	EstimatedWait int `json:"estimated_wait"` // 预估等待时间(秒)
	Status        int `json:"status"`         // 排队状态
}

// SessionStatisticsReq 会话统计请求
type SessionStatisticsReq struct {
	SiteId            uint64 `json:"site_id"`
	CompanyId         uint64 `json:"company_id"`
	CustomerServiceId uint64 `json:"customer_service_id"`
	StartDate         string `json:"start_date" v:"required#开始日期不能为空"`
	EndDate           string `json:"end_date" v:"required#结束日期不能为空"`
}

// SessionStatisticsResp 会话统计响应
type SessionStatisticsResp struct {
	TotalSessions       int     `json:"total_sessions"`        // 总会话数
	CompletedSessions   int     `json:"completed_sessions"`    // 完成会话数
	AverageWaitTime     float64 `json:"average_wait_time"`     // 平均等待时间
	AverageSessionTime  float64 `json:"average_session_time"`  // 平均会话时长
	AverageResponseTime float64 `json:"average_response_time"` // 平均响应时间
	SatisfactionRate    float64 `json:"satisfaction_rate"`     // 满意度
	TransferRate        float64 `json:"transfer_rate"`         // 转接率
	AbandonRate         float64 `json:"abandon_rate"`          // 放弃率
}

// SessionDetailResp 会话详情响应
type SessionDetailResp struct {
	Session    interface{} `json:"session"`    // 会话信息
	Queue      interface{} `json:"queue"`      // 排队信息
	Transfers  interface{} `json:"transfers"`  // 转接记录
	Evaluation interface{} `json:"evaluation"` // 评价信息
	Messages   interface{} `json:"messages"`   // 消息列表
	Visitor    interface{} `json:"visitor"`    // 访客信息
	Service    interface{} `json:"service"`    // 客服信息
}
