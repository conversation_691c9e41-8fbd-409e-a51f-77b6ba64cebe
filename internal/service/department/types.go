package department

import "kefu-server/internal/model/entity"

// CreateDepartmentReq 创建部门请求
type CreateDepartmentReq struct {
	CompanyId   uint   `json:"company_id"   v:"required#公司ID不能为空"`
	ParentId    uint   `json:"parent_id"`
	Name        string `json:"name"         v:"required#部门名称不能为空"`
	Code        string `json:"code"         v:"required#部门编码不能为空"`
	Description string `json:"description"`
	ManagerId   uint   `json:"manager_id"`
	Level       int    `json:"level"`
	Sort        int    `json:"sort"`
}

// UpdateDepartmentReq 更新部门请求
type UpdateDepartmentReq struct {
	Id          uint   `json:"id"           v:"required#部门ID不能为空"`
	CompanyId   uint   `json:"company_id"   v:"required#公司ID不能为空"`
	ParentId    uint   `json:"parent_id"`
	Name        string `json:"name"`
	Code        string `json:"code"`
	Description string `json:"description"`
	ManagerId   uint   `json:"manager_id"`
	Level       int    `json:"level"`
	Sort        int    `json:"sort"`
	Status      int    `json:"status"`
}

// GetDepartmentListReq 获取部门列表请求
type GetDepartmentListReq struct {
	CompanyId uint   `json:"company_id" v:"required#公司ID不能为空"`
	ParentId  int    `json:"parent_id"  d:"-1"`  // -1表示查询所有，0表示查询顶级部门
	Name      string `json:"name"`
	Code      string `json:"code"`
	Page      int    `json:"page"       d:"1"`
	Limit     int    `json:"limit"      d:"20"`
}

// DepartmentTreeNode 部门树节点
type DepartmentTreeNode struct {
	*entity.Department
	Children []*DepartmentTreeNode `json:"children"`
}
