package company

import (
	"context"
	"kefu-server/internal/model/entity"
)

type Service struct{}

func New() *Service {
	return &Service{}
}

// CreateCompany 创建公司
func (s *Service) CreateCompany(ctx context.Context, req interface{}) (*entity.Company, error) {
	// TODO: 实现创建公司逻辑
	return nil, nil
}

// UpdateCompany 更新公司
func (s *Service) UpdateCompany(ctx context.Context, req interface{}) error {
	// TODO: 实现更新公司逻辑
	return nil
}

// GetCompanyList 获取公司列表
func (s *Service) GetCompanyList(ctx context.Context, page, limit int, keyword string) ([]entity.Company, int, error) {
	// TODO: 实现获取公司列表逻辑
	return nil, 0, nil
}

// DeleteCompany 删除公司
func (s *Service) DeleteCompany(ctx context.Context, id uint64) error {
	// TODO: 实现删除公司逻辑
	return nil
}
