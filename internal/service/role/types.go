package role

// CreateRoleReq 创建角色请求
type CreateRoleReq struct {
	CompanyId   uint   `json:"company_id"   v:"required#公司ID不能为空"`
	Name        string `json:"name"         v:"required#角色名称不能为空"`
	Code        string `json:"code"         v:"required#角色编码不能为空"`
	Description string `json:"description"`
	Type        int    `json:"type"         v:"required#角色类型不能为空"`
	Level       int    `json:"level"`
	Permissions string `json:"permissions"`
}

// UpdateRoleReq 更新角色请求
type UpdateRoleReq struct {
	Id          uint   `json:"id"           v:"required#角色ID不能为空"`
	CompanyId   uint   `json:"company_id"   v:"required#公司ID不能为空"`
	Name        string `json:"name"`
	Code        string `json:"code"`
	Description string `json:"description"`
	Type        int    `json:"type"`
	Level       int    `json:"level"`
	Permissions string `json:"permissions"`
	Status      int    `json:"status"`
}

// GetRoleListReq 获取角色列表请求
type GetRoleListReq struct {
	CompanyId uint   `json:"company_id" v:"required#公司ID不能为空"`
	Name      string `json:"name"`
	Code      string `json:"code"`
	Type      int    `json:"type"`
	Page      int    `json:"page"       d:"1"`
	Limit     int    `json:"limit"      d:"20"`
}
