package employee

import (
	"kefu-server/internal/model/entity"

	"github.com/gogf/gf/v2/os/gtime"
)

// CreateEmployeeReq 创建员工请求
type CreateEmployeeReq struct {
	CompanyId    uint        `json:"company_id"    v:"required#公司ID不能为空"`
	DepartmentId uint        `json:"department_id" v:"required#部门ID不能为空"`
	EmployeeNo   string      `json:"employee_no"`
	Username     string      `json:"username"      v:"required#用户名不能为空"`
	Password     string      `json:"password"      v:"required#密码不能为空"`
	RealName     string      `json:"real_name"     v:"required#真实姓名不能为空"`
	Nickname     string      `json:"nickname"`
	Avatar       string      `json:"avatar"`
	Gender       int         `json:"gender"`
	Birthday     *gtime.Time `json:"birthday"`
	Phone        string      `json:"phone"`
	Email        string      `json:"email"`
	IdCard       string      `json:"id_card"`
	Address      string      `json:"address"`
	EntryDate    *gtime.Time `json:"entry_date"`
	Position     string      `json:"position"`
	Level        string      `json:"level"`
	Salary       float64     `json:"salary"`
	Remark       string      `json:"remark"`
	RoleIds      []uint      `json:"role_ids"`
}

// UpdateEmployeeReq 更新员工请求
type UpdateEmployeeReq struct {
	Id           uint        `json:"id"            v:"required#员工ID不能为空"`
	CompanyId    uint        `json:"company_id"    v:"required#公司ID不能为空"`
	DepartmentId uint        `json:"department_id"`
	EmployeeNo   string      `json:"employee_no"`
	Username     string      `json:"username"`
	Password     string      `json:"password"`
	RealName     string      `json:"real_name"`
	Nickname     string      `json:"nickname"`
	Avatar       string      `json:"avatar"`
	Gender       int         `json:"gender"`
	Birthday     *gtime.Time `json:"birthday"`
	Phone        string      `json:"phone"`
	Email        string      `json:"email"`
	IdCard       string      `json:"id_card"`
	Address      string      `json:"address"`
	EntryDate    *gtime.Time `json:"entry_date"`
	LeaveDate    *gtime.Time `json:"leave_date"`
	Position     string      `json:"position"`
	Level        string      `json:"level"`
	Salary       float64     `json:"salary"`
	Status       int         `json:"status"`
	Remark       string      `json:"remark"`
	RoleIds      []uint      `json:"role_ids"`
}

// GetEmployeeListReq 获取员工列表请求
type GetEmployeeListReq struct {
	CompanyId    uint   `json:"company_id"    v:"required#公司ID不能为空"`
	DepartmentId uint   `json:"department_id"`
	RealName     string `json:"real_name"`
	Username     string `json:"username"`
	Phone        string `json:"phone"`
	Email        string `json:"email"`
	Position     string `json:"position"`
	Page         int    `json:"page"          d:"1"`
	Limit        int    `json:"limit"         d:"20"`
}

// EmployeeWithRoles 员工及其角色信息
type EmployeeWithRoles struct {
	*entity.Employee
	Roles []*entity.Role `json:"roles"`
}

// RegisterUserReq 用户注册请求
type RegisterUserReq struct {
	CompanyName     string `json:"company_name"     v:"required#公司名称不能为空"`
	Username        string `json:"username"         v:"required#用户账号不能为空"`
	Password        string `json:"password"         v:"required|length:6,20#密码不能为空|密码长度为6-20位"`
	ConfirmPassword string `json:"confirm_password" v:"required|same:password#确认密码不能为空|两次密码输入不一致"`
	RealName        string `json:"real_name"        v:"required#真实姓名不能为空"`
	Phone           string `json:"phone"`
	Email           string `json:"email"`
}

// RegisterUserRes 用户注册响应
type RegisterUserRes struct {
	CompanyId uint   `json:"company_id"`
	UserId    uint   `json:"user_id"`
	Username  string `json:"username"`
	RealName  string `json:"real_name"`
}
