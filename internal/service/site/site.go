package site

import (
	"context"
	"kefu-server/internal/dao"
	"kefu-server/internal/model/entity"
	"kefu-server/internal/model/response"

	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/grand"
)

type Service struct{}

func New() *Service {
	return &Service{}
}

// GetSiteConfigBySiteKey 根据站点密钥获取站点配置
func (s *Service) GetSiteConfigBySiteKey(ctx context.Context, siteKey string) (*response.SiteConfigResponse, error) {
	// 根据站点密钥查找站点
	site, err := dao.Site.Ctx(ctx).Where("site_key", siteKey).Where("status", 1).One()
	if err != nil {
		return nil, err
	}
	if site.IsEmpty() {
		return nil, nil
	}

	var siteEntity entity.Site
	if err := site.Struct(&siteEntity); err != nil {
		return nil, err
	}

	// 获取站点设置
	siteSettings, err := s.getSiteSettings(ctx, siteEntity.Id)
	if err != nil {
		return nil, err
	}

	// 获取邀请弹窗设置
	invitePopupSettings, err := s.getInvitePopupSettings(ctx, siteEntity.Id)
	if err != nil {
		return nil, err
	}

	// 获取咨询图标设置
	consultIconSettings, err := s.getConsultIconSettings(ctx, siteEntity.Id)
	if err != nil {
		return nil, err
	}

	// 获取快捷对话设置
	quickChatSettings, err := s.getQuickChatSettings(ctx, siteEntity.Id)
	if err != nil {
		return nil, err
	}

	// 获取独立对话设置
	independentChatSettings, err := s.getIndependentChatSettings(ctx, siteEntity.Id)
	if err != nil {
		return nil, err
	}

	// 获取访客留言设置
	visitorMessageSettings, err := s.getVisitorMessageSettings(ctx, siteEntity.Id)
	if err != nil {
		return nil, err
	}

	// 获取在线客服列表
	onlineCustomerServices, err := s.getOnlineCustomerServices(ctx, siteEntity.Id)
	if err != nil {
		return nil, err
	}

	return &response.SiteConfigResponse{
		Site:                    &siteEntity,
		SiteSettings:            siteSettings,
		InvitePopupSettings:     invitePopupSettings,
		ConsultIconSettings:     consultIconSettings,
		QuickChatSettings:       quickChatSettings,
		IndependentChatSettings: independentChatSettings,
		VisitorMessageSettings:  visitorMessageSettings,
		OnlineCustomerServices:  onlineCustomerServices,
	}, nil
}

// CreateSite 创建站点
func (s *Service) CreateSite(ctx context.Context, req interface{}) (*entity.Site, error) {
	// 生成站点密钥
	_ = s.generateSiteKey()

	// 创建站点记录
	// 这里需要根据具体的req结构来实现
	// 暂时返回nil
	return nil, nil
}

// UpdateSite 更新站点
func (s *Service) UpdateSite(ctx context.Context, req interface{}) error {
	// 实现站点更新逻辑
	return nil
}

// GetSiteList 获取站点列表
func (s *Service) GetSiteList(ctx context.Context, companyId uint64, page, limit int) ([]entity.Site, int, error) {
	// 实现获取站点列表逻辑
	return nil, 0, nil
}

// DeleteSite 删除站点
func (s *Service) DeleteSite(ctx context.Context, id uint64) error {
	// 实现删除站点逻辑
	return nil
}

// 私有方法

func (s *Service) getSiteSettings(ctx context.Context, siteId uint64) (*entity.SiteSettings, error) {
	result, err := dao.SiteSettings.Ctx(ctx).Where("site_id", siteId).One()
	if err != nil {
		return nil, err
	}
	if result.IsEmpty() {
		return nil, nil
	}

	var settings entity.SiteSettings
	if err := result.Struct(&settings); err != nil {
		return nil, err
	}
	return &settings, nil
}

func (s *Service) getInvitePopupSettings(ctx context.Context, siteId uint64) (*entity.InvitePopupSettings, error) {
	result, err := dao.InvitePopupSettings.Ctx(ctx).Where("site_id", siteId).One()
	if err != nil {
		return nil, err
	}
	if result.IsEmpty() {
		return nil, nil
	}

	var settings entity.InvitePopupSettings
	if err := result.Struct(&settings); err != nil {
		return nil, err
	}
	return &settings, nil
}

func (s *Service) getConsultIconSettings(ctx context.Context, siteId uint64) (*entity.ConsultIconSettings, error) {
	result, err := dao.ConsultIconSettings.Ctx(ctx).Where("site_id", siteId).One()
	if err != nil {
		return nil, err
	}
	if result.IsEmpty() {
		return nil, nil
	}

	var settings entity.ConsultIconSettings
	if err := result.Struct(&settings); err != nil {
		return nil, err
	}
	return &settings, nil
}

func (s *Service) getQuickChatSettings(ctx context.Context, siteId uint64) (*entity.QuickChatSettings, error) {
	result, err := dao.QuickChatSettings.Ctx(ctx).Where("site_id", siteId).One()
	if err != nil {
		return nil, err
	}
	if result.IsEmpty() {
		return nil, nil
	}

	var settings entity.QuickChatSettings
	if err := result.Struct(&settings); err != nil {
		return nil, err
	}
	return &settings, nil
}

func (s *Service) getIndependentChatSettings(ctx context.Context, siteId uint64) (*entity.IndependentChatSettings, error) {
	result, err := dao.IndependentChatSettings.Ctx(ctx).Where("site_id", siteId).One()
	if err != nil {
		return nil, err
	}
	if result.IsEmpty() {
		return nil, nil
	}

	var settings entity.IndependentChatSettings
	if err := result.Struct(&settings); err != nil {
		return nil, err
	}
	return &settings, nil
}

func (s *Service) getVisitorMessageSettings(ctx context.Context, siteId uint64) (*entity.VisitorMessageSettings, error) {
	result, err := dao.VisitorMessageSettings.Ctx(ctx).Where("site_id", siteId).One()
	if err != nil {
		return nil, err
	}
	if result.IsEmpty() {
		return nil, nil
	}

	var settings entity.VisitorMessageSettings
	if err := result.Struct(&settings); err != nil {
		return nil, err
	}
	return &settings, nil
}

func (s *Service) getOnlineCustomerServices(ctx context.Context, siteId uint64) ([]response.CustomerServiceInfo, error) {
	// 查询在线席位
	agents, err := dao.Agent.Ctx(ctx).Where("status", 1).All()
	if err != nil {
		return nil, err
	}

	var result []response.CustomerServiceInfo
	for _, agent := range agents {
		result = append(result, response.CustomerServiceInfo{
			Id:     agent["id"].Uint64(),
			Status: agent["status"].Int(),
		})
	}

	return result, nil
}

func (s *Service) generateSiteKey() string {
	// 生成32位随机字符串作为站点密钥
	randomStr := grand.S(16)
	timestamp := gtime.Now().String()
	return gmd5.MustEncrypt(randomStr + timestamp)
}
