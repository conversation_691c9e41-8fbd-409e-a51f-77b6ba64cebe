-- 数据库测试脚本
-- 用于验证组织架构模块的数据库表和数据是否正确

USE `kefu_server`;

-- ========================================
-- 测试查询脚本
-- ========================================

-- 1. 查看所有表
SHOW TABLES LIKE 'ks_%';

-- 2. 查看公司信息
SELECT * FROM ks_company;

-- 3. 查看部门结构（树形结构）
SELECT 
    d.id,
    d.name AS dept_name,
    d.code AS dept_code,
    d.level,
    d.parent_id,
    pd.name AS parent_name,
    e.real_name AS manager_name
FROM ks_department d
LEFT JOIN ks_department pd ON d.parent_id = pd.id
LEFT JOIN ks_employee e ON d.manager_id = e.id
WHERE d.company_id = 1 AND d.deleted_at IS NULL
ORDER BY d.level, d.sort;

-- 4. 查看角色信息
SELECT 
    id,
    name,
    code,
    description,
    type,
    level,
    permissions,
    status
FROM ks_role 
WHERE company_id = 1 AND deleted_at IS NULL
ORDER BY level, id;

-- 5. 查看员工信息及其角色
SELECT 
    e.id,
    e.employee_no,
    e.username,
    e.real_name,
    e.nickname,
    e.phone,
    e.email,
    e.position,
    e.level,
    e.salary,
    d.name AS dept_name,
    GROUP_CONCAT(r.name ORDER BY r.level) AS roles
FROM ks_employee e
LEFT JOIN ks_department d ON e.department_id = d.id
LEFT JOIN ks_employee_role er ON e.id = er.employee_id
LEFT JOIN ks_role r ON er.role_id = r.id
WHERE e.company_id = 1 AND e.deleted_at IS NULL
GROUP BY e.id
ORDER BY e.id;

-- 6. 查看部门员工统计
SELECT 
    d.name AS dept_name,
    d.code AS dept_code,
    COUNT(e.id) AS employee_count,
    AVG(e.salary) AS avg_salary
FROM ks_department d
LEFT JOIN ks_employee e ON d.id = e.department_id AND e.deleted_at IS NULL
WHERE d.company_id = 1 AND d.deleted_at IS NULL
GROUP BY d.id, d.name, d.code
ORDER BY d.level, d.sort;

-- 7. 查看角色分配统计
SELECT 
    r.name AS role_name,
    r.code AS role_code,
    r.level AS role_level,
    COUNT(er.employee_id) AS employee_count
FROM ks_role r
LEFT JOIN ks_employee_role er ON r.id = er.role_id
WHERE r.company_id = 1 AND r.deleted_at IS NULL
GROUP BY r.id, r.name, r.code, r.level
ORDER BY r.level, r.id;

-- 8. 查看座席信息
SELECT * FROM ks_seat WHERE company_id = 1;

-- 9. 查看站点信息
SELECT * FROM ks_site WHERE company_id = 1;

-- 10. 验证数据完整性
-- 检查是否有孤立的员工（部门不存在）
SELECT 
    e.id,
    e.real_name,
    e.department_id,
    '部门不存在' AS issue
FROM ks_employee e
LEFT JOIN ks_department d ON e.department_id = d.id
WHERE e.company_id = 1 
    AND e.deleted_at IS NULL 
    AND d.id IS NULL;

-- 检查是否有孤立的角色分配（员工或角色不存在）
SELECT 
    er.id,
    er.employee_id,
    er.role_id,
    '员工或角色不存在' AS issue
FROM ks_employee_role er
LEFT JOIN ks_employee e ON er.employee_id = e.id
LEFT JOIN ks_role r ON er.role_id = r.id
WHERE (e.id IS NULL OR r.id IS NULL);

-- 11. 查看表结构信息
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS,
    DATA_LENGTH,
    INDEX_LENGTH
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'kefu_server' 
    AND TABLE_NAME LIKE 'ks_%'
ORDER BY TABLE_NAME;

-- 12. 查看索引信息
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'kefu_server' 
    AND TABLE_NAME IN ('ks_department', 'ks_employee', 'ks_role', 'ks_employee_role')
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- ========================================
-- 性能测试查询
-- ========================================

-- 测试部门树查询性能
EXPLAIN SELECT 
    d1.id,
    d1.name,
    d2.name AS parent_name,
    d3.name AS grandparent_name
FROM ks_department d1
LEFT JOIN ks_department d2 ON d1.parent_id = d2.id
LEFT JOIN ks_department d3 ON d2.parent_id = d3.id
WHERE d1.company_id = 1 AND d1.deleted_at IS NULL;

-- 测试员工角色查询性能
EXPLAIN SELECT 
    e.real_name,
    GROUP_CONCAT(r.name) AS roles
FROM ks_employee e
LEFT JOIN ks_employee_role er ON e.id = er.employee_id
LEFT JOIN ks_role r ON er.role_id = r.id
WHERE e.company_id = 1 AND e.deleted_at IS NULL
GROUP BY e.id;

-- ========================================
-- 数据验证
-- ========================================

-- 验证必要的数据是否存在
SELECT 
    '公司数据' AS data_type,
    COUNT(*) AS count,
    CASE WHEN COUNT(*) > 0 THEN '✓' ELSE '✗' END AS status
FROM ks_company
UNION ALL
SELECT 
    '部门数据' AS data_type,
    COUNT(*) AS count,
    CASE WHEN COUNT(*) > 0 THEN '✓' ELSE '✗' END AS status
FROM ks_department WHERE deleted_at IS NULL
UNION ALL
SELECT 
    '角色数据' AS data_type,
    COUNT(*) AS count,
    CASE WHEN COUNT(*) > 0 THEN '✓' ELSE '✗' END AS status
FROM ks_role WHERE deleted_at IS NULL
UNION ALL
SELECT 
    '员工数据' AS data_type,
    COUNT(*) AS count,
    CASE WHEN COUNT(*) > 0 THEN '✓' ELSE '✗' END AS status
FROM ks_employee WHERE deleted_at IS NULL
UNION ALL
SELECT 
    '角色分配数据' AS data_type,
    COUNT(*) AS count,
    CASE WHEN COUNT(*) > 0 THEN '✓' ELSE '✗' END AS status
FROM ks_employee_role;
