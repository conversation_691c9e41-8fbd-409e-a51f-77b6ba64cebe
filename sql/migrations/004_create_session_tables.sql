-- 访客会话表
CREATE TABLE IF NOT EXISTS `ks_visitor_session` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '会话ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话唯一标识',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `company_id` bigint(20) unsigned NOT NULL COMMENT '公司ID',
  `visitor_id` varchar(64) NOT NULL COMMENT '访客唯一标识',
  `agent_id` bigint(20) unsigned DEFAULT NULL COMMENT '分配的席位ID',
  `skill_group_id` bigint(20) unsigned DEFAULT NULL COMMENT '技能组ID',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '会话状态：0-等待中，1-进行中，2-已结束，3-已转接，4-超时结束',
  `priority` tinyint(1) NOT NULL DEFAULT '2' COMMENT '优先级：1-低，2-中，3-高，4-紧急',
  `source` varchar(20) DEFAULT 'web' COMMENT '来源：web,mobile,api',
  `visitor_info` json COMMENT '访客信息(JSON格式)',
  `start_time` datetime DEFAULT NULL COMMENT '会话开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '会话结束时间',
  `first_response_time` datetime DEFAULT NULL COMMENT '首次响应时间',
  `last_activity_time` datetime DEFAULT NULL COMMENT '最后活动时间',
  `duration` int(11) NOT NULL DEFAULT '0' COMMENT '会话持续时间(秒)',
  `message_count` int(11) NOT NULL DEFAULT '0' COMMENT '消息总数',
  `visitor_message_count` int(11) NOT NULL DEFAULT '0' COMMENT '访客消息数',
  `service_message_count` int(11) NOT NULL DEFAULT '0' COMMENT '客服消息数',
  `queue_time` int(11) NOT NULL DEFAULT '0' COMMENT '排队时间(秒)',
  `wait_time` int(11) NOT NULL DEFAULT '0' COMMENT '等待时间(秒)',
  `satisfaction` tinyint(1) DEFAULT NULL COMMENT '满意度评分：1-5分',
  `satisfaction_comment` text COMMENT '满意度评价',
  `tags` json COMMENT '标签(JSON格式)',
  `remarks` text COMMENT '备注',
  `transfer_reason` varchar(255) DEFAULT NULL COMMENT '转接原因',
  `end_reason` varchar(255) DEFAULT NULL COMMENT '结束原因',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_id` (`session_id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_visitor_id` (`visitor_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_skill_group_id` (`skill_group_id`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客会话表';

-- 会话排队表
CREATE TABLE IF NOT EXISTS `ks_session_queue` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '排队ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `company_id` bigint(20) unsigned NOT NULL COMMENT '公司ID',
  `visitor_id` varchar(64) NOT NULL COMMENT '访客ID',
  `skill_group_id` bigint(20) unsigned DEFAULT NULL COMMENT '技能组ID',
  `priority` tinyint(1) NOT NULL DEFAULT '2' COMMENT '优先级',
  `queue_position` int(11) NOT NULL DEFAULT '0' COMMENT '排队位置',
  `estimated_wait` int(11) NOT NULL DEFAULT '0' COMMENT '预估等待时间(秒)',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '排队状态：0-排队中，1-已分配，2-已取消，3-超时',
  `join_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入排队时间',
  `assign_time` datetime DEFAULT NULL COMMENT '分配时间',
  `leave_time` datetime DEFAULT NULL COMMENT '离开排队时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_id` (`session_id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_visitor_id` (`visitor_id`),
  KEY `idx_skill_group_id` (`skill_group_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_queue_position` (`queue_position`),
  KEY `idx_join_time` (`join_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话排队表';

-- 会话转接记录表
CREATE TABLE IF NOT EXISTS `ks_session_transfer` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '转接ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `from_agent_id` bigint(20) unsigned NOT NULL COMMENT '原席位ID',
  `to_agent_id` bigint(20) unsigned NOT NULL COMMENT '目标席位ID',
  `from_skill_group_id` bigint(20) unsigned DEFAULT NULL COMMENT '原技能组ID',
  `to_skill_group_id` bigint(20) unsigned DEFAULT NULL COMMENT '目标技能组ID',
  `transfer_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '转接类型：1-客服转接，2-技能组转接，3-系统转接',
  `transfer_reason` varchar(255) DEFAULT NULL COMMENT '转接原因',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '转接状态：0-待接受，1-已接受，2-已拒绝，3-超时',
  `transfer_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '转接时间',
  `accept_time` datetime DEFAULT NULL COMMENT '接受时间',
  `reject_reason` varchar(255) DEFAULT NULL COMMENT '拒绝原因',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_from_agent_id` (`from_agent_id`),
  KEY `idx_to_agent_id` (`to_agent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_transfer_time` (`transfer_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话转接记录表';

-- 会话评价表
CREATE TABLE IF NOT EXISTS `ks_session_evaluation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `visitor_id` varchar(64) NOT NULL COMMENT '访客ID',
  `agent_id` bigint(20) unsigned NOT NULL COMMENT '席位ID',
  `overall_score` tinyint(1) NOT NULL COMMENT '总体评分：1-5分',
  `service_score` tinyint(1) DEFAULT NULL COMMENT '服务评分：1-5分',
  `response_score` tinyint(1) DEFAULT NULL COMMENT '响应评分：1-5分',
  `professional_score` tinyint(1) DEFAULT NULL COMMENT '专业评分：1-5分',
  `comment` text COMMENT '评价内容',
  `tags` json COMMENT '评价标签(JSON格式)',
  `is_anonymous` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否匿名：1-是，0-否',
  `evaluation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评价时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_visitor` (`session_id`, `visitor_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_visitor_id` (`visitor_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_overall_score` (`overall_score`),
  KEY `idx_evaluation_time` (`evaluation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话评价表';
