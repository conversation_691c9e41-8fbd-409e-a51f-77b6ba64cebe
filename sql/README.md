# 客服系统数据库脚本说明

## 文件结构

```
sql/
├── README.md                           # 本说明文件
├── init_database.sql                   # 完整数据库初始化脚本
├── test_database.sql                   # 数据库测试验证脚本
└── migrations/                         # 数据库迁移脚本目录
    ├── 001_create_tables.sql          # 基础表创建脚本
    ├── 002_create_visitor_tables.sql  # 访客相关表（已更新为Agent模块）
    ├── 003_create_chat_tables.sql     # 聊天相关表（已更新为Agent模块）
    ├── 004_create_session_tables.sql  # 会话相关表（已更新为Agent模块）
    ├── 005_create_organization_tables.sql  # 组织架构表
    ├── 006_insert_organization_data.sql    # 组织架构初始数据
    ├── 007_create_invite_record_table.sql  # 邀请记录表（已更新为Agent模块）
    └── 008_migrate_customer_service_to_agent.sql # CustomerService迁移到Agent模块
```

## 快速开始

### 方式一：使用完整初始化脚本（推荐）

```bash
# 直接执行完整初始化脚本
mysql -u root -p < sql/init_database.sql
```

### 方式二：按迁移脚本顺序执行

```bash
# 按顺序执行迁移脚本
mysql -u root -p < sql/migrations/001_create_tables.sql
mysql -u root -p < sql/migrations/002_create_visitor_tables.sql
mysql -u root -p < sql/migrations/003_create_chat_tables.sql
mysql -u root -p < sql/migrations/004_create_session_tables.sql
mysql -u root -p < sql/migrations/005_create_organization_tables.sql
mysql -u root -p < sql/migrations/006_insert_organization_data.sql
mysql -u root -p < sql/migrations/007_create_invite_record_table.sql
mysql -u root -p < sql/migrations/008_migrate_customer_service_to_agent.sql
```

## 数据库表结构

### 组织架构相关表

#### 1. ks_company - 公司表
- 存储公司基本信息
- 包含公司名称、介绍、地址、联系方式等

#### 2. ks_department - 部门表
- 支持多层级部门结构
- 包含部门编码、名称、描述、经理等信息
- 支持软删除

#### 3. ks_role - 角色表
- 支持系统角色和自定义角色
- 包含权限配置（JSON格式）
- 支持角色级别管理

#### 4. ks_employee - 员工表
- 员工基本信息管理
- 关联部门和公司
- 支持员工工号、职位、薪资等信息
- 支持软删除

#### 5. ks_employee_role - 员工角色关联表
- 多对多关系表
- 支持一个员工拥有多个角色

### 业务相关表

#### 6. ks_agent - 席位表
- 客服席位管理
- 关联公司、站点、技能组、员工
- 包含状态、并发数、购买时间、过期时间等信息

#### 7. ks_site - 站点表
- 网站站点管理
- 每个站点有唯一的site_key

#### 8. ks_visitor - 访客表
- 访客信息管理
- 包含访客行为统计

#### 9. ks_chat_message - 聊天消息表
- 聊天记录存储
- 支持多种消息类型（文本、图片、文件等）

## 默认数据

### 默认公司
- 公司ID: 1
- 公司名称: 示例科技有限公司

### 默认部门结构
```
总经理办公室 (CEO_OFFICE)
技术部 (TECH_DEPT)
├── 前端开发组 (FRONTEND_GROUP)
├── 后端开发组 (BACKEND_GROUP)
└── 测试组 (TEST_GROUP)
市场部 (MARKET_DEPT)
客服部 (SERVICE_DEPT)
├── 在线客服组 (ONLINE_SERVICE_GROUP)
└── 电话客服组 (PHONE_SERVICE_GROUP)
人事部 (HR_DEPT)
财务部 (FINANCE_DEPT)
```

### 默认角色
1. 超级管理员 (SUPER_ADMIN) - 系统角色
2. 总经理 (CEO) - 自定义角色
3. 部门经理 (DEPT_MANAGER) - 自定义角色
4. 技术经理 (TECH_MANAGER) - 自定义角色
5. 客服经理 (SERVICE_MANAGER) - 自定义角色
6. 高级工程师 (SENIOR_ENGINEER) - 自定义角色
7. 工程师 (ENGINEER) - 自定义角色
8. 高级客服 (SENIOR_SERVICE) - 自定义角色
9. 客服专员 (SERVICE_SPECIALIST) - 自定义角色
10. 人事专员 (HR_SPECIALIST) - 自定义角色
11. 财务专员 (FINANCE_SPECIALIST) - 自定义角色
12. 市场专员 (MARKET_SPECIALIST) - 自定义角色

### 默认员工账号
| 用户名 | 密码 | 姓名 | 部门 | 角色 |
|--------|------|------|------|------|
| admin | 123456 | 张总 | 总经理办公室 | 超级管理员、总经理 |
| tech_manager | 123456 | 李技术 | 技术部 | 技术经理、高级工程师 |
| service_manager | 123456 | 王客服 | 客服部 | 客服经理、高级客服 |
| frontend_dev | 123456 | 赵前端 | 前端开发组 | 工程师 |
| backend_dev | 123456 | 钱后端 | 后端开发组 | 工程师 |
| customer_service1 | 123456 | 孙客服 | 在线客服组 | 客服专员 |
| customer_service2 | 123456 | 周客服 | 在线客服组 | 客服专员 |
| hr_specialist | 123456 | 吴人事 | 人事部 | 人事专员 |
| finance_specialist | 123456 | 郑财务 | 财务部 | 财务专员 |
| market_specialist | 123456 | 冯市场 | 市场部 | 市场专员 |

## 注意事项

1. **密码安全**: 默认密码为 `123456`，生产环境请及时修改
2. **外键约束**: 外键约束默认被注释，根据需要启用
3. **软删除**: 部分表支持软删除，删除时只更新 `deleted_at` 字段
4. **权限配置**: 角色权限采用JSON格式存储，便于扩展
5. **数据完整性**: 使用 `INSERT IGNORE` 避免重复插入

## 数据库配置

确保MySQL配置支持：
- 字符集: utf8mb4
- 排序规则: utf8mb4_unicode_ci
- 时区设置正确

## 备份与恢复

### 备份
```bash
mysqldump -u root -p kefu_server > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 恢复
```bash
mysql -u root -p kefu_server < backup_file.sql
```

## 数据库测试

### 运行测试脚本
```bash
# 执行测试脚本验证数据库结构和数据
mysql -u root -p kefu_server < sql/test_database.sql
```

### 测试内容
测试脚本包含以下验证：

1. **表结构验证**
   - 检查所有组织架构相关表是否存在
   - 验证表结构和索引

2. **数据完整性验证**
   - 检查默认数据是否正确插入
   - 验证外键关系的完整性
   - 查找孤立数据

3. **功能测试查询**
   - 部门树形结构查询
   - 员工角色关联查询
   - 统计分析查询

4. **性能测试**
   - 复杂查询的执行计划分析
   - 索引使用情况检查

### 预期结果
正确执行后应该看到：
- ✓ 公司数据：1条记录
- ✓ 部门数据：11条记录
- ✓ 角色数据：12条记录
- ✓ 员工数据：10条记录
- ✓ 角色分配数据：13条记录

## 常见问题

### Q: 外键约束导致删除失败
A: 外键约束默认被注释，如需启用请谨慎操作，确保数据依赖关系正确。

### Q: 字符编码问题
A: 确保MySQL配置使用utf8mb4字符集，支持完整的Unicode字符。

### Q: 权限不足
A: 确保MySQL用户有CREATE、INSERT、UPDATE、DELETE权限。

### Q: 表已存在错误
A: 脚本使用`CREATE TABLE IF NOT EXISTS`和`INSERT IGNORE`，重复执行是安全的。
