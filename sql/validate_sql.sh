#!/bin/bash

# SQL文件语法验证脚本
# 用于检查SQL文件的基本语法和结构

echo "==================================="
echo "客服系统数据库脚本验证"
echo "==================================="

# 检查文件是否存在
check_file() {
    local file=$1
    if [ -f "$file" ]; then
        echo "✓ $file 存在"
        return 0
    else
        echo "✗ $file 不存在"
        return 1
    fi
}

# 检查SQL语法（基本检查）
check_sql_syntax() {
    local file=$1
    echo "检查 $file 的SQL语法..."
    
    # 检查是否有基本的SQL关键字
    if grep -q "CREATE TABLE" "$file"; then
        echo "  ✓ 包含CREATE TABLE语句"
    fi
    
    if grep -q "INSERT" "$file"; then
        echo "  ✓ 包含INSERT语句"
    fi
    
    # 检查是否有未闭合的括号
    local open_parens=$(grep -o "(" "$file" | wc -l)
    local close_parens=$(grep -o ")" "$file" | wc -l)
    
    if [ "$open_parens" -eq "$close_parens" ]; then
        echo "  ✓ 括号匹配正确 ($open_parens 对)"
    else
        echo "  ✗ 括号不匹配 (开: $open_parens, 闭: $close_parens)"
    fi
    
    # 检查是否有分号结尾
    if grep -q ";" "$file"; then
        echo "  ✓ 包含SQL语句分隔符"
    fi
    
    echo ""
}

# 检查表结构定义
check_table_structure() {
    local file=$1
    echo "检查 $file 的表结构定义..."
    
    # 检查组织架构相关表
    local tables=("ks_company" "ks_department" "ks_employee" "ks_role" "ks_employee_role")
    
    for table in "${tables[@]}"; do
        if grep -q "CREATE TABLE.*$table" "$file"; then
            echo "  ✓ 包含 $table 表定义"
        else
            echo "  - 未包含 $table 表定义"
        fi
    done
    
    echo ""
}

# 检查数据插入
check_data_insertion() {
    local file=$1
    echo "检查 $file 的数据插入..."
    
    # 检查是否有INSERT语句
    local insert_count=$(grep -c "INSERT" "$file")
    echo "  ✓ 包含 $insert_count 个INSERT语句"
    
    # 检查是否使用了INSERT IGNORE
    if grep -q "INSERT IGNORE" "$file"; then
        echo "  ✓ 使用了INSERT IGNORE防止重复插入"
    fi
    
    echo ""
}

echo "开始验证SQL文件..."
echo ""

# 验证主要文件
echo "1. 验证完整初始化脚本"
if check_file "init_database.sql"; then
    check_sql_syntax "init_database.sql"
    check_table_structure "init_database.sql"
    check_data_insertion "init_database.sql"
fi

echo "2. 验证组织架构表脚本"
if check_file "migrations/005_create_organization_tables.sql"; then
    check_sql_syntax "migrations/005_create_organization_tables.sql"
    check_table_structure "migrations/005_create_organization_tables.sql"
fi

echo "3. 验证组织架构数据脚本"
if check_file "migrations/006_insert_organization_data.sql"; then
    check_sql_syntax "migrations/006_insert_organization_data.sql"
    check_data_insertion "migrations/006_insert_organization_data.sql"
fi

echo "4. 验证测试脚本"
if check_file "test_database.sql"; then
    check_sql_syntax "test_database.sql"
fi

# 检查文件编码
echo "5. 检查文件编码"
for file in init_database.sql migrations/005_create_organization_tables.sql migrations/006_insert_organization_data.sql test_database.sql; do
    if [ -f "$file" ]; then
        encoding=$(file -b --mime-encoding "$file")
        echo "  $file: $encoding"
    fi
done

echo ""
echo "==================================="
echo "验证完成"
echo "==================================="

# 显示文件大小统计
echo ""
echo "文件大小统计:"
for file in init_database.sql migrations/005_create_organization_tables.sql migrations/006_insert_organization_data.sql test_database.sql; do
    if [ -f "$file" ]; then
        size=$(wc -c < "$file")
        lines=$(wc -l < "$file")
        echo "  $file: $size 字节, $lines 行"
    fi
done

echo ""
echo "建议："
echo "1. 在生产环境使用前，请先在测试环境验证"
echo "2. 备份现有数据库后再执行脚本"
echo "3. 根据实际需求调整默认数据"
echo "4. 考虑启用外键约束以保证数据完整性"
